<template>
  <div class="todo-list">
    <div class="list-header" v-if="todos.length > 0">
      <div class="batch-actions">
        <label class="select-all-wrapper">
          <input
            type="checkbox"
            :checked="allSelected"
            :indeterminate="someSelected && !allSelected"
            @change="toggleSelectAll"
            class="select-all-checkbox"
          />
          <span>全选</span>
        </label>
        <div class="batch-buttons" v-if="selectedTodos.length > 0">
          <button @click="batchComplete" class="batch-btn complete">
            ✅ 完成选中 ({{ selectedTodos.length }})
          </button>
          <button @click="batchDelete" class="batch-btn delete">
            🗑️ 删除选中 ({{ selectedTodos.length }})
          </button>
        </div>
      </div>
      <div class="drag-hint" v-if="!isDragging">
        💡 提示：拖拽任务可以重新排序
      </div>
    </div>

    <div
      ref="sortableContainer"
      class="todo-items"
      :class="{ dragging: isDragging }"
    >
      <TodoItem
        v-for="todo in todos"
        :key="todo.id"
        :todo="todo"
        :selected="selectedTodos.includes(todo.id)"
        @toggle="$emit('toggle-todo', todo.id)"
        @update="$emit('update-todo', todo.id, $event)"
        @delete="$emit('delete-todo', todo.id)"
        @select="toggleSelect(todo.id)"
        class="sortable-item"
        :data-id="todo.id"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useSortable } from '@vueuse/integrations/useSortable'
import TodoItem from './TodoItem.vue'

const props = defineProps({
  todos: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['toggle-todo', 'update-todo', 'delete-todo', 'reorder'])

const sortableContainer = ref()
const selectedTodos = ref([])
const isDragging = ref(false)

// 批量选择相关计算属性
const allSelected = computed(() =>
  props.todos.length > 0 && selectedTodos.value.length === props.todos.length
)

const someSelected = computed(() =>
  selectedTodos.value.length > 0 && selectedTodos.value.length < props.todos.length
)

// 拖拽排序
const { start, stop } = useSortable(sortableContainer, props.todos, {
  animation: 200,
  ghostClass: 'ghost',
  chosenClass: 'chosen',
  dragClass: 'drag',
  onStart: () => {
    isDragging.value = true
  },
  onEnd: (evt) => {
    isDragging.value = false
    if (evt.oldIndex !== evt.newIndex) {
      emit('reorder', {
        oldIndex: evt.oldIndex,
        newIndex: evt.newIndex
      })
    }
  }
})

// 批量操作方法
const toggleSelect = (todoId) => {
  const index = selectedTodos.value.indexOf(todoId)
  if (index > -1) {
    selectedTodos.value.splice(index, 1)
  } else {
    selectedTodos.value.push(todoId)
  }
}

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedTodos.value = []
  } else {
    selectedTodos.value = props.todos.map(todo => todo.id)
  }
}

const batchComplete = () => {
  selectedTodos.value.forEach(todoId => {
    const todo = props.todos.find(t => t.id === todoId)
    if (todo && !todo.completed) {
      emit('toggle-todo', todoId)
    }
  })
  selectedTodos.value = []
}

const batchDelete = () => {
  if (confirm(`确定要删除选中的 ${selectedTodos.value.length} 个任务吗？`)) {
    selectedTodos.value.forEach(todoId => {
      emit('delete-todo', todoId)
    })
    selectedTodos.value = []
  }
}

// 监听todos变化，清理无效的选中项
watch(() => props.todos, (newTodos) => {
  const validIds = newTodos.map(todo => todo.id)
  selectedTodos.value = selectedTodos.value.filter(id => validIds.includes(id))
}, { deep: true })

onMounted(() => {
  start()
})

onUnmounted(() => {
  stop()
})
</script>

<style scoped>
.todo-list {
  width: 100%;
}

.list-header {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.batch-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.select-all-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #495057;
}

.select-all-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.batch-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.batch-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.batch-btn.complete {
  background: #d4edda;
  color: #155724;
}

.batch-btn.complete:hover {
  background: #c3e6cb;
}

.batch-btn.delete {
  background: #f8d7da;
  color: #721c24;
}

.batch-btn.delete:hover {
  background: #f5c6cb;
}

.drag-hint {
  font-size: 0.85rem;
  color: #6c757d;
  text-align: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #dee2e6;
}

.todo-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.todo-items.dragging {
  user-select: none;
}

.sortable-item {
  cursor: move;
  transition: all 0.2s ease;
}

.sortable-item:hover {
  transform: translateY(-1px);
}

/* 拖拽状态样式 */
.sortable-item.ghost {
  opacity: 0.5;
  background: #f8f9fa;
  transform: rotate(2deg);
}

.sortable-item.chosen {
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.3);
  border-color: #007bff;
}

.sortable-item.drag {
  transform: rotate(5deg);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* 动画效果 */
.todo-enter-active,
.todo-leave-active {
  transition: all 0.3s ease;
}

.todo-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.todo-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.todo-move {
  transition: transform 0.3s ease;
}

@media (max-width: 768px) {
  .list-header {
    padding: 12px;
  }

  .batch-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .batch-buttons {
    justify-content: center;
  }

  .drag-hint {
    font-size: 0.8rem;
  }
}
</style>
