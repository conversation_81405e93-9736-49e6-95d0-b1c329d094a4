<template>
  <div class="add-todo">
    <form @submit.prevent="handleSubmit" class="add-todo-form">
      <div class="form-header">
        <h2>📝 添加新任务</h2>
        <button
          type="button"
          @click="toggleExpanded"
          class="toggle-btn"
          :class="{ expanded: isExpanded }"
        >
          {{ isExpanded ? '收起' : '展开' }}
        </button>
      </div>

      <div class="form-content">
        <!-- 标题输入 -->
        <div class="input-group">
          <input
            v-model="form.title"
            type="text"
            placeholder="输入任务标题..."
            class="title-input"
            required
            ref="titleInput"
          />
        </div>

        <!-- 展开的表单内容 -->
        <Transition name="expand">
          <div v-if="isExpanded" class="expanded-form">
            <div class="input-group">
              <textarea
                v-model="form.description"
                placeholder="任务描述（可选）"
                class="description-input"
                rows="3"
              ></textarea>
            </div>

            <div class="input-group">
              <label class="input-label">优先级</label>
              <select v-model="form.priority" class="priority-select">
                <option value="1">🟢 低优先级</option>
                <option value="2">🟡 中优先级</option>
                <option value="3">🔴 高优先级</option>
              </select>
            </div>
          </div>
        </Transition>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <button
            type="submit"
            class="btn btn-primary"
            :disabled="!form.title.trim()"
          >
            ➕ 添加任务
          </button>
          <button
            v-if="hasContent"
            type="button"
            @click="clearForm"
            class="btn btn-secondary"
          >
            🗑️ 清空
          </button>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from 'vue'

const emit = defineEmits(['add-todo'])

const isExpanded = ref(false)
const titleInput = ref(null)

const form = reactive({
  title: '',
  description: '',
  priority: 2
})

const hasContent = computed(() => {
  return form.title.trim() || form.description.trim()
})

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const handleSubmit = () => {
  if (form.title.trim()) {
    emit('add-todo', {
      title: form.title.trim(),
      description: form.description.trim(),
      priority: parseInt(form.priority)
    })
    clearForm()
    focusTitle()
  }
}

const clearForm = () => {
  form.title = ''
  form.description = ''
  form.priority = 2
}

const focusTitle = () => {
  nextTick(() => {
    titleInput.value?.focus()
  })
}
</script>

<style scoped>
.add-todo {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.add-todo-form {
  width: 100%;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.form-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.toggle-btn {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  color: #6c757d;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.toggle-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.toggle-btn.expanded {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.input-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
}

.title-input,
.description-input,
.priority-select {
  padding: 12px 16px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.title-input:focus,
.description-input:focus,
.priority-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.title-input {
  font-weight: 500;
}

.description-input {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.priority-select {
  cursor: pointer;
}

/* 展开动画 */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.expand-enter-to,
.expand-leave-from {
  opacity: 1;
  max-height: 200px;
  transform: translateY(0);
}

.expanded-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .add-todo {
    padding: 16px;
  }
  
  .form-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .form-header h2 {
    text-align: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    justify-content: center;
  }
}
</style>
