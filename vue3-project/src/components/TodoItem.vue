<template>
  <div class="todo-item" :class="{ completed: todo.completed, editing: isEditing, selected: selected }">
    <div class="todo-content">
      <!-- 选择复选框 -->
      <label class="select-wrapper">
        <input
          type="checkbox"
          :checked="selected"
          @change="$emit('select')"
          class="select-checkbox"
        />
        <span class="select-checkmark"></span>
      </label>

      <!-- 完成状态复选框 -->
      <label class="checkbox-wrapper">
        <input
          type="checkbox"
          :checked="todo.completed"
          @change="$emit('toggle')"
          class="checkbox"
        />
        <span class="checkmark"></span>
      </label>

      <!-- 内容区域 -->
      <div class="todo-info" v-if="!isEditing">
        <div class="todo-header">
          <h3 class="todo-title" :class="{ completed: todo.completed }">
            {{ todo.title }}
          </h3>
          <div class="priority-badge" :class="`priority-${todo.priority}`">
            {{ getPriorityText(todo.priority) }}
          </div>
        </div>
        <p v-if="todo.description" class="todo-description">
          {{ todo.description }}
        </p>
        <div class="todo-meta">
          <span class="created-time">
            创建于 {{ formatDate(todo.createdAt) }}
          </span>
          <span v-if="todo.updatedAt !== todo.createdAt" class="updated-time">
            更新于 {{ formatDate(todo.updatedAt) }}
          </span>
        </div>
      </div>

      <!-- 编辑模式 -->
      <div class="todo-edit" v-else>
        <input
          v-model="editForm.title"
          class="edit-title"
          placeholder="任务标题"
          @keyup.enter="saveEdit"
          @keyup.escape="cancelEdit"
          ref="titleInput"
        />
        <textarea
          v-model="editForm.description"
          class="edit-description"
          placeholder="任务描述（可选）"
          rows="2"
        ></textarea>
        <select v-model="editForm.priority" class="edit-priority">
          <option value="1">低优先级</option>
          <option value="2">中优先级</option>
          <option value="3">高优先级</option>
        </select>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="todo-actions">
      <template v-if="!isEditing">
        <button @click="startEdit" class="btn btn-edit" title="编辑">
          ✏️
        </button>
        <button @click="$emit('delete')" class="btn btn-delete" title="删除">
          🗑️
        </button>
      </template>
      <template v-else>
        <button @click="saveEdit" class="btn btn-save" title="保存">
          ✅
        </button>
        <button @click="cancelEdit" class="btn btn-cancel" title="取消">
          ❌
        </button>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'

const props = defineProps({
  todo: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['toggle', 'update', 'delete', 'select'])

const isEditing = ref(false)
const titleInput = ref(null)
const editForm = reactive({
  title: '',
  description: '',
  priority: 1
})

const getPriorityText = (priority) => {
  const map = { 1: '低', 2: '中', 3: '高' }
  return map[priority] || '低'
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const startEdit = () => {
  editForm.title = props.todo.title
  editForm.description = props.todo.description
  editForm.priority = props.todo.priority
  isEditing.value = true
  
  nextTick(() => {
    titleInput.value?.focus()
  })
}

const saveEdit = () => {
  if (editForm.title.trim()) {
    emit('update', {
      title: editForm.title.trim(),
      description: editForm.description.trim(),
      priority: parseInt(editForm.priority)
    })
    isEditing.value = false
  }
}

const cancelEdit = () => {
  isEditing.value = false
}
</script>

<style scoped>
.todo-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.todo-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-color: #dee2e6;
}

.todo-item.completed {
  opacity: 0.7;
  background: #f8f9fa;
}

.todo-item.editing {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.todo-item.selected {
  border-color: #28a745;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
  background: #f8fff9;
}

.todo-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* 选择复选框样式 */
.select-wrapper {
  position: relative;
  cursor: pointer;
  margin-top: 2px;
}

.select-checkbox {
  opacity: 0;
  position: absolute;
}

.select-checkmark {
  display: block;
  width: 18px;
  height: 18px;
  border: 2px solid #28a745;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.select-checkbox:checked + .select-checkmark {
  background: #28a745;
  border-color: #28a745;
}

.select-checkbox:checked + .select-checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

/* 完成状态复选框样式 */
.checkbox-wrapper {
  position: relative;
  cursor: pointer;
  margin-top: 2px;
}

.checkbox {
  opacity: 0;
  position: absolute;
}

.checkmark {
  display: block;
  width: 20px;
  height: 20px;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.checkbox:checked + .checkmark {
  background: #28a745;
  border-color: #28a745;
}

.checkbox:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 内容样式 */
.todo-info {
  flex: 1;
}

.todo-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.todo-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  transition: all 0.2s ease;
}

.todo-title.completed {
  text-decoration: line-through;
  color: #6c757d;
}

.priority-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.priority-1 {
  background: #d1ecf1;
  color: #0c5460;
}

.priority-2 {
  background: #fff3cd;
  color: #856404;
}

.priority-3 {
  background: #f8d7da;
  color: #721c24;
}

.todo-description {
  margin: 8px 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.todo-meta {
  display: flex;
  gap: 12px;
  font-size: 0.8rem;
  color: #adb5bd;
  margin-top: 8px;
}

/* 编辑模式样式 */
.todo-edit {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.edit-title,
.edit-description,
.edit-priority {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
}

.edit-title:focus,
.edit-description:focus,
.edit-priority:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.edit-description {
  resize: vertical;
  min-height: 60px;
}

/* 操作按钮 */
.todo-actions {
  display: flex;
  gap: 4px;
}

.btn {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  background: transparent;
}

.btn:hover {
  background: #f8f9fa;
}

.btn-delete:hover {
  background: #f8d7da;
}

.btn-save:hover {
  background: #d4edda;
}

.btn-cancel:hover {
  background: #f8d7da;
}

@media (max-width: 768px) {
  .todo-item {
    padding: 12px;
  }
  
  .todo-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .todo-meta {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
