<template>
  <div class="filter-bar">
    <div class="filter-section">
      <h3 class="section-title">📊 筛选和排序</h3>
      
      <div class="filter-controls">
        <!-- 状态过滤 -->
        <div class="control-group">
          <label class="control-label">状态筛选</label>
          <div class="filter-buttons">
            <button
              v-for="option in filterOptions"
              :key="option.value"
              @click="updateFilter(option.value)"
              class="filter-btn"
              :class="{ active: filter === option.value }"
            >
              {{ option.icon }} {{ option.label }}
              <span class="count">{{ getFilterCount(option.value) }}</span>
            </button>
          </div>
        </div>

        <!-- 排序选择 -->
        <div class="control-group">
          <label class="control-label">排序方式</label>
          <select
            :value="sortBy"
            @change="updateSortBy($event.target.value)"
            class="sort-select"
          >
            <option value="created">📅 按创建时间</option>
            <option value="priority">⭐ 按优先级</option>
            <option value="title">🔤 按标题</option>
          </select>
        </div>

        <!-- 批量操作 -->
        <div class="control-group" v-if="stats.completed > 0">
          <label class="control-label">批量操作</label>
          <button
            @click="$emit('clear-completed')"
            class="clear-btn"
            :title="`清除 ${stats.completed} 个已完成任务`"
          >
            🗑️ 清除已完成 ({{ stats.completed }})
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索功能 -->
    <div class="search-section">
      <div class="search-wrapper">
        <input
          v-model="searchQuery"
          @input="handleSearch"
          type="text"
          placeholder="🔍 搜索任务..."
          class="search-input"
        />
        <button
          v-if="searchQuery"
          @click="clearSearch"
          class="clear-search-btn"
        >
          ❌
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  filter: {
    type: String,
    required: true
  },
  sortBy: {
    type: String,
    required: true
  },
  stats: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:filter', 'update:sort-by', 'clear-completed', 'search'])

const searchQuery = ref('')

const filterOptions = [
  { value: 'all', label: '全部', icon: '📋' },
  { value: 'active', label: '待完成', icon: '⏳' },
  { value: 'completed', label: '已完成', icon: '✅' }
]

const getFilterCount = (filterValue) => {
  switch (filterValue) {
    case 'all':
      return props.stats.total
    case 'active':
      return props.stats.active
    case 'completed':
      return props.stats.completed
    default:
      return 0
  }
}

const updateFilter = (newFilter) => {
  emit('update:filter', newFilter)
}

const updateSortBy = (newSortBy) => {
  emit('update:sort-by', newSortBy)
}

const handleSearch = () => {
  emit('search', searchQuery.value)
}

const clearSearch = () => {
  searchQuery.value = ''
  emit('search', '')
}
</script>

<style scoped>
.filter-bar {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.filter-section {
  margin-bottom: 20px;
}

.section-title {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.filter-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  border-radius: 20px;
  background: white;
  color: #6c757d;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.filter-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 500;
}

.filter-btn.active .count {
  background: rgba(255, 255, 255, 0.3);
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: white;
  color: #495057;
  cursor: pointer;
  font-size: 0.9rem;
  max-width: 200px;
}

.sort-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.clear-btn {
  padding: 8px 16px;
  border: 1px solid #dc3545;
  border-radius: 6px;
  background: white;
  color: #dc3545;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  max-width: fit-content;
}

.clear-btn:hover {
  background: #dc3545;
  color: white;
}

.search-section {
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
}

.search-wrapper {
  position: relative;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  padding-right: 40px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.clear-search-btn:hover {
  background: #f8f9fa;
}

@media (max-width: 768px) {
  .filter-bar {
    padding: 16px;
  }
  
  .filter-controls {
    gap: 12px;
  }
  
  .filter-buttons {
    gap: 6px;
  }
  
  .filter-btn {
    padding: 6px 12px;
    font-size: 0.85rem;
  }
  
  .sort-select {
    max-width: 100%;
  }
  
  .search-wrapper {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .filter-buttons {
    flex-direction: column;
  }
  
  .filter-btn {
    justify-content: center;
  }
}
</style>
