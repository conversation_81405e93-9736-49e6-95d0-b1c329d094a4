<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import TodoList from './components/TodoList.vue'
import AddTodo from './components/AddTodo.vue'
import FilterBar from './components/FilterBar.vue'

// 数据结构定义
const todos = ref([])
const filter = ref('all') // 'all', 'active', 'completed'
const sortBy = ref('created') // 'created', 'priority', 'title'
const searchQuery = ref('')

// 计算属性
const filteredTodos = computed(() => {
  let filtered = todos.value

  // 按状态过滤
  if (filter.value === 'active') {
    filtered = filtered.filter(todo => !todo.completed)
  } else if (filter.value === 'completed') {
    filtered = filtered.filter(todo => todo.completed)
  }

  // 按搜索关键词过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.filter(todo =>
      todo.title.toLowerCase().includes(query) ||
      todo.description.toLowerCase().includes(query)
    )
  }

  // 排序
  filtered.sort((a, b) => {
    if (sortBy.value === 'priority') {
      return b.priority - a.priority
    } else if (sortBy.value === 'title') {
      return a.title.localeCompare(b.title)
    } else {
      return new Date(b.createdAt) - new Date(a.createdAt)
    }
  })

  return filtered
})

const todoStats = computed(() => ({
  total: todos.value.length,
  completed: todos.value.filter(todo => todo.completed).length,
  active: todos.value.filter(todo => !todo.completed).length
}))

// 方法
const addTodo = (todoData) => {
  const newTodo = {
    id: Date.now().toString(),
    title: todoData.title,
    description: todoData.description || '',
    completed: false,
    priority: todoData.priority || 1, // 1: 低, 2: 中, 3: 高
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  todos.value.unshift(newTodo)
  saveTodos()
}

const updateTodo = (id, updates) => {
  const index = todos.value.findIndex(todo => todo.id === id)
  if (index !== -1) {
    todos.value[index] = {
      ...todos.value[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }
    saveTodos()
  }
}

const deleteTodo = (id) => {
  const index = todos.value.findIndex(todo => todo.id === id)
  if (index !== -1) {
    todos.value.splice(index, 1)
    saveTodos()
  }
}

const toggleTodo = (id) => {
  const todo = todos.value.find(todo => todo.id === id)
  if (todo) {
    updateTodo(id, { completed: !todo.completed })
  }
}

const clearCompleted = () => {
  todos.value = todos.value.filter(todo => !todo.completed)
  saveTodos()
}

// 本地存储
const saveTodos = () => {
  localStorage.setItem('todos', JSON.stringify(todos.value))
}

const loadTodos = () => {
  const saved = localStorage.getItem('todos')
  if (saved) {
    todos.value = JSON.parse(saved)
  } else {
    // 添加一些示例数据
    todos.value = [
      {
        id: '1',
        title: '完成Vue3待办应用开发',
        description: '使用Vue3、Vite和现代CSS创建一个功能完整的待办事项应用',
        completed: false,
        priority: 3,
        createdAt: new Date(Date.now() - 86400000).toISOString(),
        updatedAt: new Date(Date.now() - 86400000).toISOString()
      },
      {
        id: '2',
        title: '学习Vue3 Composition API',
        description: '深入了解Vue3的新特性和最佳实践',
        completed: true,
        priority: 2,
        createdAt: new Date(Date.now() - 172800000).toISOString(),
        updatedAt: new Date(Date.now() - 86400000).toISOString()
      },
      {
        id: '3',
        title: '添加拖拽排序功能',
        description: '使用SortableJS实现任务的拖拽重排序',
        completed: false,
        priority: 2,
        createdAt: new Date(Date.now() - 3600000).toISOString(),
        updatedAt: new Date(Date.now() - 3600000).toISOString()
      },
      {
        id: '4',
        title: '优化移动端体验',
        description: '确保应用在移动设备上的良好表现',
        completed: false,
        priority: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
    saveTodos()
  }
}

const handleSearch = (query) => {
  searchQuery.value = query
}

const handleReorder = ({ oldIndex, newIndex }) => {
  const item = todos.value.splice(oldIndex, 1)[0]
  todos.value.splice(newIndex, 0, item)
  saveTodos()
}

// 生命周期
onMounted(() => {
  loadTodos()
})
</script>

<template>
  <div class="app">
    <header class="app-header">
      <h1>📝 待办事项清单</h1>
      <div class="stats">
        <span class="stat">总计: {{ todoStats.total }}</span>
        <span class="stat">待完成: {{ todoStats.active }}</span>
        <span class="stat">已完成: {{ todoStats.completed }}</span>
      </div>
    </header>

    <main class="app-main">
      <AddTodo @add-todo="addTodo" />

      <FilterBar
        v-model:filter="filter"
        v-model:sort-by="sortBy"
        :stats="todoStats"
        @clear-completed="clearCompleted"
        @search="handleSearch"
      />

      <TodoList
        :todos="filteredTodos"
        @toggle-todo="toggleTodo"
        @update-todo="updateTodo"
        @delete-todo="deleteTodo"
        @reorder="handleReorder"
      />

      <div v-if="todos.length === 0" class="empty-state">
        <p>🎉 暂无待办事项，添加一个开始吧！</p>
      </div>
    </main>
  </div>
</template>

<style scoped>
.app {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-header {
  text-align: center;
  margin-bottom: 30px;
}

.app-header h1 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 2.5rem;
}

.stats {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.stat {
  background: #f8f9fa;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.app-main {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .app {
    padding: 15px;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .stats {
    gap: 10px;
  }

  .stat {
    font-size: 0.8rem;
    padding: 6px 12px;
  }
}
</style>
