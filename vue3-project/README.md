# 📝 待办事件清单应用

一个功能完整、界面美观的待办事项管理应用，基于 Vue 3 + Vite 构建。

## ✨ 功能特性

### 🎯 核心功能
- ✅ **任务管理** - 添加、编辑、删除、完成任务
- 🏷️ **优先级设置** - 高、中、低三个优先级
- 📝 **详细描述** - 为任务添加详细说明
- 💾 **本地存储** - 数据持久化，刷新不丢失

### 🔍 高级功能
- 🎛️ **智能筛选** - 按状态筛选（全部/待完成/已完成）
- 📊 **多种排序** - 按创建时间、优先级、标题排序
- 🔎 **实时搜索** - 搜索任务标题和描述
- 📦 **批量操作** - 批量选择、完成、删除任务
- 🎪 **拖拽排序** - 直观的拖拽重排序功能
- 🧹 **一键清理** - 快速清除已完成任务

### 🎨 用户体验
- 📱 **响应式设计** - 完美适配桌面端和移动端
- 🎭 **流畅动画** - 丰富的交互动画效果
- 🎯 **直观操作** - 简洁明了的用户界面
- 🌈 **现代设计** - 美观的视觉设计

## 🚀 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 🛠️ 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **拖拽功能**: @vueuse/integrations + SortableJS
- **样式**: 原生 CSS + CSS Variables
- **数据存储**: localStorage

## 📱 使用指南

### 添加任务
1. 在顶部输入框中输入任务标题
2. 点击"展开"查看更多选项
3. 添加任务描述和设置优先级
4. 点击"添加任务"按钮

### 管理任务
- **完成任务**: 点击任务左侧的圆形复选框
- **编辑任务**: 点击任务右侧的编辑按钮（✏️）
- **删除任务**: 点击任务右侧的删除按钮（🗑️）
- **拖拽排序**: 直接拖拽任务卡片重新排序

### 筛选和搜索
- **状态筛选**: 使用"全部"、"待完成"、"已完成"按钮
- **排序**: 在下拉菜单中选择排序方式
- **搜索**: 在搜索框中输入关键词

### 批量操作
1. 点击任务左侧的绿色选择框
2. 选择多个任务后，使用批量操作按钮
3. 可以批量完成或删除选中的任务

## 🎯 项目结构

```
src/
├── components/
│   ├── AddTodo.vue      # 添加任务组件
│   ├── TodoList.vue     # 任务列表组件
│   ├── TodoItem.vue     # 单个任务组件
│   └── FilterBar.vue    # 筛选栏组件
├── App.vue              # 主应用组件
├── main.js              # 应用入口
└── style.css            # 全局样式
```

## 🔧 开发说明

### 数据结构
```javascript
{
  id: String,           // 唯一标识
  title: String,        // 任务标题
  description: String,  // 任务描述
  completed: Boolean,   // 完成状态
  priority: Number,     // 优先级 (1:低, 2:中, 3:高)
  createdAt: String,    // 创建时间
  updatedAt: String     // 更新时间
}
```

### 主要功能实现
- **响应式数据**: 使用 Vue 3 的 `ref` 和 `reactive`
- **计算属性**: 用于筛选和排序逻辑
- **本地存储**: 自动保存到 localStorage
- **拖拽排序**: 集成 SortableJS 库
- **组件通信**: 使用 props 和 emits

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**享受高效的任务管理体验！** 🎉
